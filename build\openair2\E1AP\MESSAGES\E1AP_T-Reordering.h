/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-IEs"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#ifndef	_E1AP_T_Reordering_H_
#define	_E1AP_T_Reordering_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum E1AP_T_Reordering {
	E1AP_T_Reordering_ms0	= 0,
	E1AP_T_Reordering_ms1	= 1,
	E1AP_T_Reordering_ms2	= 2,
	E1AP_T_Reordering_ms4	= 3,
	E1AP_T_Reordering_ms5	= 4,
	E1AP_T_Reordering_ms8	= 5,
	E1AP_T_Reordering_ms10	= 6,
	E1AP_T_Reordering_ms15	= 7,
	E1AP_T_Reordering_ms20	= 8,
	E1AP_T_Reordering_ms30	= 9,
	E1AP_T_Reordering_ms40	= 10,
	E1AP_T_Reordering_ms50	= 11,
	E1AP_T_Reordering_ms60	= 12,
	E1AP_T_Reordering_ms80	= 13,
	E1AP_T_Reordering_ms100	= 14,
	E1AP_T_Reordering_ms120	= 15,
	E1AP_T_Reordering_ms140	= 16,
	E1AP_T_Reordering_ms160	= 17,
	E1AP_T_Reordering_ms180	= 18,
	E1AP_T_Reordering_ms200	= 19,
	E1AP_T_Reordering_ms220	= 20,
	E1AP_T_Reordering_ms240	= 21,
	E1AP_T_Reordering_ms260	= 22,
	E1AP_T_Reordering_ms280	= 23,
	E1AP_T_Reordering_ms300	= 24,
	E1AP_T_Reordering_ms500	= 25,
	E1AP_T_Reordering_ms750	= 26,
	E1AP_T_Reordering_ms1000	= 27,
	E1AP_T_Reordering_ms1250	= 28,
	E1AP_T_Reordering_ms1500	= 29,
	E1AP_T_Reordering_ms1750	= 30,
	E1AP_T_Reordering_ms2000	= 31,
	E1AP_T_Reordering_ms2250	= 32,
	E1AP_T_Reordering_ms2500	= 33,
	E1AP_T_Reordering_ms2750	= 34,
	E1AP_T_Reordering_ms3000	= 35
	/*
	 * Enumeration is extensible
	 */
} e_E1AP_T_Reordering;

/* E1AP_T-Reordering */
typedef long	 E1AP_T_Reordering_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_E1AP_T_Reordering_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_T_Reordering;
extern const asn_INTEGER_specifics_t asn_SPC_E1AP_T_Reordering_specs_1;
asn_struct_free_f E1AP_T_Reordering_free;
asn_struct_print_f E1AP_T_Reordering_print;
asn_constr_check_f E1AP_T_Reordering_constraint;
xer_type_decoder_f E1AP_T_Reordering_decode_xer;
xer_type_encoder_f E1AP_T_Reordering_encode_xer;
per_type_decoder_f E1AP_T_Reordering_decode_uper;
per_type_encoder_f E1AP_T_Reordering_encode_uper;
per_type_decoder_f E1AP_T_Reordering_decode_aper;
per_type_encoder_f E1AP_T_Reordering_encode_aper;

#ifdef __cplusplus
}
#endif

#endif	/* _E1AP_T_Reordering_H_ */
#include <asn_internal.h>
