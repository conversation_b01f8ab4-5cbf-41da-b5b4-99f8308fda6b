/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-IEs"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#ifndef	_E1AP_ULDataSplitThreshold_H_
#define	_E1AP_ULDataSplitThreshold_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum E1AP_ULDataSplitThreshold {
	E1AP_ULDataSplitThreshold_b0	= 0,
	E1AP_ULDataSplitThreshold_b100	= 1,
	E1AP_ULDataSplitThreshold_b200	= 2,
	E1AP_ULDataSplitThreshold_b400	= 3,
	E1AP_ULDataSplitThreshold_b800	= 4,
	E1AP_ULDataSplitThreshold_b1600	= 5,
	E1AP_ULDataSplitThreshold_b3200	= 6,
	E1AP_ULDataSplitThreshold_b6400	= 7,
	E1AP_ULDataSplitThreshold_b12800	= 8,
	E1AP_ULDataSplitThreshold_b25600	= 9,
	E1AP_ULDataSplitThreshold_b51200	= 10,
	E1AP_ULDataSplitThreshold_b102400	= 11,
	E1AP_ULDataSplitThreshold_b204800	= 12,
	E1AP_ULDataSplitThreshold_b409600	= 13,
	E1AP_ULDataSplitThreshold_b819200	= 14,
	E1AP_ULDataSplitThreshold_b1228800	= 15,
	E1AP_ULDataSplitThreshold_b1638400	= 16,
	E1AP_ULDataSplitThreshold_b2457600	= 17,
	E1AP_ULDataSplitThreshold_b3276800	= 18,
	E1AP_ULDataSplitThreshold_b4096000	= 19,
	E1AP_ULDataSplitThreshold_b4915200	= 20,
	E1AP_ULDataSplitThreshold_b5734400	= 21,
	E1AP_ULDataSplitThreshold_b6553600	= 22,
	E1AP_ULDataSplitThreshold_infinity	= 23
	/*
	 * Enumeration is extensible
	 */
} e_E1AP_ULDataSplitThreshold;

/* E1AP_ULDataSplitThreshold */
typedef long	 E1AP_ULDataSplitThreshold_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_E1AP_ULDataSplitThreshold_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ULDataSplitThreshold;
extern const asn_INTEGER_specifics_t asn_SPC_E1AP_ULDataSplitThreshold_specs_1;
asn_struct_free_f E1AP_ULDataSplitThreshold_free;
asn_struct_print_f E1AP_ULDataSplitThreshold_print;
asn_constr_check_f E1AP_ULDataSplitThreshold_constraint;
xer_type_decoder_f E1AP_ULDataSplitThreshold_decode_xer;
xer_type_encoder_f E1AP_ULDataSplitThreshold_encode_xer;
per_type_decoder_f E1AP_ULDataSplitThreshold_decode_uper;
per_type_encoder_f E1AP_ULDataSplitThreshold_encode_uper;
per_type_decoder_f E1AP_ULDataSplitThreshold_decode_aper;
per_type_encoder_f E1AP_ULDataSplitThreshold_encode_aper;

#ifdef __cplusplus
}
#endif

#endif	/* _E1AP_ULDataSplitThreshold_H_ */
#include <asn_internal.h>
