/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-IEs"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#ifndef	_E1AP_TSCTrafficCharacteristics_H_
#define	_E1AP_TSCTrafficCharacteristics_H_


#include <asn_application.h>

/* Including external dependencies */
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct E1AP_TSCTrafficInformation;
struct E1AP_ProtocolExtensionContainer;

/* E1AP_TSCTrafficCharacteristics */
typedef struct E1AP_TSCTrafficCharacteristics {
	struct E1AP_TSCTrafficInformation	*tSCTrafficCharacteristicsUL;	/* OPTIONAL */
	struct E1AP_TSCTrafficInformation	*tSCTrafficCharacteristicsDL;	/* OPTIONAL */
	struct E1AP_ProtocolExtensionContainer	*iE_Extensions;	/* OPTIONAL */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_TSCTrafficCharacteristics_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_E1AP_TSCTrafficCharacteristics;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_TSCTrafficCharacteristics_specs_1;
extern asn_TYPE_member_t asn_MBR_E1AP_TSCTrafficCharacteristics_1[3];

#ifdef __cplusplus
}
#endif

#endif	/* _E1AP_TSCTrafficCharacteristics_H_ */
#include <asn_internal.h>
